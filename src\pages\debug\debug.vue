<template>
  <view class="debug-page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <view class="nav-left" @click="goBack">
          <text class="back-icon">←</text>
        </view>
        <view class="nav-title">设备调试</view>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 设备状态 -->
    <view class="status-section">
      <view class="status-card">
        <view class="status-header">
          <text class="status-title">设备连接状态</text>
          <view class="status-indicator" :class="{ connected: isConnected }">
            <text class="status-text">{{ isConnected ? '已连接' : '未连接' }}</text>
          </view>
        </view>
        <view class="device-info" v-if="isConnected">
          <view class="info-item">
            <text class="info-label">设备名称：</text>
            <text class="info-value">{{ deviceInfo.name }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">设备ID：</text>
            <text class="info-value">{{ deviceInfo.id }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">固件版本：</text>
            <text class="info-value">{{ deviceInfo.version }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 调试工具 -->
    <view class="tools-section">
      <view class="section-title">调试工具</view>
      
      <view class="tool-group">
        <view class="tool-item" @click="scanDevice">
          <view class="tool-icon">📡</view>
          <view class="tool-content">
            <text class="tool-title">扫描设备</text>
            <text class="tool-desc">搜索附近的可连接设备</text>
          </view>
        </view>
        
        <view class="tool-item" @click="connectDevice">
          <view class="tool-icon">🔗</view>
          <view class="tool-content">
            <text class="tool-title">连接设备</text>
            <text class="tool-desc">建立与设备的通信连接</text>
          </view>
        </view>
        
        <view class="tool-item" @click="testConnection">
          <view class="tool-icon">🧪</view>
          <view class="tool-content">
            <text class="tool-title">连接测试</text>
            <text class="tool-desc">测试设备连接稳定性</text>
          </view>
        </view>
        
        <view class="tool-item" @click="sendCommand">
          <view class="tool-icon">📤</view>
          <view class="tool-content">
            <text class="tool-title">发送指令</text>
            <text class="tool-desc">向设备发送调试指令</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 日志输出 -->
    <view class="log-section">
      <view class="section-title">调试日志</view>
      <view class="log-container">
        <scroll-view class="log-content" scroll-y :scroll-top="scrollTop">
          <view class="log-item" v-for="(log, index) in debugLogs" :key="index">
            <text class="log-time">{{ log.time }}</text>
            <text class="log-message" :class="log.type">{{ log.message }}</text>
          </view>
        </scroll-view>
        <view class="log-actions">
          <button class="clear-btn" @click="clearLogs">清空日志</button>
          <button class="export-btn" @click="exportLogs">导出日志</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// 设备信息接口
interface DeviceInfo {
  name: string
  id: string
  version: string
}

// 日志接口
interface DebugLog {
  time: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
}

// 响应式数据
const isConnected = ref(false)
const scrollTop = ref(0)
const deviceInfo = reactive<DeviceInfo>({
  name: '',
  id: '',
  version: ''
})
const debugLogs = ref<DebugLog[]>([])

// 添加日志
const addLog = (message: string, type: DebugLog['type'] = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  debugLogs.value.push({
    time,
    message,
    type
  })
  
  // 自动滚动到底部
  setTimeout(() => {
    scrollTop.value = debugLogs.value.length * 100
  }, 100)
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 扫描设备
const scanDevice = () => {
  addLog('开始扫描设备...', 'info')
  
  uni.showLoading({
    title: '扫描中...'
  })
  
  setTimeout(() => {
    uni.hideLoading()
    addLog('发现设备: BUX-Device-001', 'success')
    addLog('发现设备: BUX-Device-002', 'success')
    addLog('扫描完成，共发现 2 个设备', 'info')
  }, 2000)
}

// 连接设备
const connectDevice = () => {
  if (isConnected.value) {
    addLog('设备已连接，无需重复连接', 'warning')
    return
  }
  
  addLog('正在连接设备...', 'info')
  
  uni.showLoading({
    title: '连接中...'
  })
  
  setTimeout(() => {
    uni.hideLoading()
    isConnected.value = true
    deviceInfo.name = 'BUX-Device-001'
    deviceInfo.id = 'BUX001234567890'
    deviceInfo.version = 'v2.1.3'
    
    addLog('设备连接成功', 'success')
    addLog(`设备名称: ${deviceInfo.name}`, 'info')
    addLog(`设备ID: ${deviceInfo.id}`, 'info')
    addLog(`固件版本: ${deviceInfo.version}`, 'info')
    
    uni.showToast({
      title: '连接成功',
      icon: 'success'
    })
  }, 2000)
}

// 测试连接
const testConnection = () => {
  if (!isConnected.value) {
    addLog('请先连接设备', 'error')
    uni.showToast({
      title: '请先连接设备',
      icon: 'none'
    })
    return
  }
  
  addLog('开始连接测试...', 'info')
  
  let testCount = 0
  const maxTests = 5
  
  const runTest = () => {
    testCount++
    addLog(`测试 ${testCount}/${maxTests}: 发送心跳包...`, 'info')
    
    setTimeout(() => {
      const success = Math.random() > 0.2 // 80% 成功率
      if (success) {
        addLog(`测试 ${testCount}/${maxTests}: 响应正常 (${Math.floor(Math.random() * 50 + 10)}ms)`, 'success')
      } else {
        addLog(`测试 ${testCount}/${maxTests}: 响应超时`, 'error')
      }
      
      if (testCount < maxTests) {
        setTimeout(runTest, 1000)
      } else {
        addLog('连接测试完成', 'info')
      }
    }, 500)
  }
  
  runTest()
}

// 发送指令
const sendCommand = () => {
  if (!isConnected.value) {
    addLog('请先连接设备', 'error')
    uni.showToast({
      title: '请先连接设备',
      icon: 'none'
    })
    return
  }
  
  uni.showModal({
    title: '发送指令',
    content: '请选择要发送的指令',
    showCancel: true,
    confirmText: '状态查询',
    cancelText: '重启设备',
    success: (res) => {
      if (res.confirm) {
        addLog('发送指令: GET_STATUS', 'info')
        setTimeout(() => {
          addLog('设备响应: {"status":"online","battery":85,"temperature":23.5}', 'success')
        }, 500)
      } else if (res.cancel) {
        addLog('发送指令: RESTART', 'info')
        setTimeout(() => {
          addLog('设备响应: 重启中...', 'warning')
          isConnected.value = false
          setTimeout(() => {
            addLog('设备已断开连接', 'error')
          }, 2000)
        }, 500)
      }
    }
  })
}

// 清空日志
const clearLogs = () => {
  debugLogs.value = []
  scrollTop.value = 0
  uni.showToast({
    title: '日志已清空',
    icon: 'success'
  })
}

// 导出日志
const exportLogs = () => {
  if (debugLogs.value.length === 0) {
    uni.showToast({
      title: '暂无日志可导出',
      icon: 'none'
    })
    return
  }
  
  uni.showToast({
    title: '导出功能开发中',
    icon: 'none'
  })
}

// 初始化日志
addLog('设备调试工具已启动', 'info')
addLog('请先扫描并连接设备', 'info')
</script>

<style lang="scss" scoped>
.debug-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  .nav-bar {
    display: flex;
    align-items: center;
    height: 88rpx;
    padding: 0 32rpx;
    
    .nav-left {
      width: 80rpx;
      
      .back-icon {
        font-size: 36rpx;
        color: #333;
        font-weight: bold;
      }
    }
    
    .nav-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
    
    .nav-right {
      width: 80rpx;
    }
  }
}

.status-section {
  padding: 32rpx;
  
  .status-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    
    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      
      .status-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }
      
      .status-indicator {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        background: #f5f5f5;
        
        &.connected {
          background: #e8f5e8;
          color: #52c41a;
        }
        
        .status-text {
          font-size: 24rpx;
          font-weight: 500;
        }
      }
    }
    
    .device-info {
      .info-item {
        display: flex;
        margin-bottom: 16rpx;
        
        .info-label {
          font-size: 28rpx;
          color: #666;
          width: 160rpx;
        }
        
        .info-value {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }
      }
    }
  }
}

.tools-section {
  padding: 0 32rpx 32rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
  
  .tool-group {
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    
    .tool-item {
      display: flex;
      align-items: center;
      padding: 32rpx;
      border-bottom: 2rpx solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background: #f8f9fa;
      }
      
      .tool-icon {
        width: 80rpx;
        height: 80rpx;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        margin-right: 24rpx;
      }
      
      .tool-content {
        flex: 1;
        
        .tool-title {
          display: block;
          font-size: 30rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .tool-desc {
          display: block;
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }
}

.log-section {
  padding: 0 32rpx 32rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
  
  .log-container {
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    
    .log-content {
      height: 400rpx;
      padding: 24rpx;
      background: #1e1e1e;
      
      .log-item {
        display: flex;
        margin-bottom: 16rpx;
        font-family: 'Courier New', monospace;
        
        .log-time {
          color: #888;
          font-size: 24rpx;
          margin-right: 16rpx;
          min-width: 120rpx;
        }
        
        .log-message {
          color: #fff;
          font-size: 24rpx;
          line-height: 1.4;
          
          &.success {
            color: #52c41a;
          }
          
          &.warning {
            color: #faad14;
          }
          
          &.error {
            color: #ff4d4f;
          }
        }
      }
    }
    
    .log-actions {
      display: flex;
      padding: 24rpx;
      gap: 16rpx;
      
      .clear-btn, .export-btn {
        flex: 1;
        padding: 16rpx 0;
        border-radius: 8rpx;
        font-size: 28rpx;
        border: none;
      }
      
      .clear-btn {
        background: #f5f5f5;
        color: #666;
      }
      
      .export-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
      }
    }
  }
}
</style>
