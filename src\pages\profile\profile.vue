<template>
  <view class="profile-page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <view class="nav-left" @click="goBack">
          <text class="back-icon">←</text>
        </view>
        <view class="nav-title">我的</view>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 用户信息区域 -->
    <view class="user-section">
      <view class="user-info">
        <view class="avatar-container" @click="chooseAvatar">
          <image 
            class="user-avatar" 
            :src="userInfo.avatar || '/static/logo.png'" 
            mode="aspectFill"
          />
          <view class="avatar-edit">
            <text class="edit-icon">📷</text>
          </view>
        </view>
        <view class="user-details">
          <view class="user-name">{{ userInfo.name || '未登录' }}</view>
          <view class="user-desc">{{ userInfo.desc || '点击登录获取更多功能' }}</view>
        </view>
      </view>
      
      <!-- 登录按钮 -->
      <view class="login-section" v-if="!isLoggedIn">
        <button class="login-btn" @click="handleLogin">
          <text class="login-text">立即登录</text>
        </button>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @click="contactUs">
          <view class="menu-icon">📞</view>
          <view class="menu-content">
            <text class="menu-title">联系我们</text>
            <text class="menu-desc">获取技术支持和服务咨询</text>
          </view>
          <view class="menu-arrow">›</view>
        </view>
        
        <view class="menu-item" @click="deviceDebug">
          <view class="menu-icon">🔧</view>
          <view class="menu-content">
            <text class="menu-title">设备调试</text>
            <text class="menu-desc">设备连接和调试工具</text>
          </view>
          <view class="menu-arrow">›</view>
        </view>
      </view>
      
      <view class="menu-group" v-if="isLoggedIn">
        <view class="menu-item" @click="userSettings">
          <view class="menu-icon">⚙️</view>
          <view class="menu-content">
            <text class="menu-title">设置</text>
            <text class="menu-desc">个人信息和应用设置</text>
          </view>
          <view class="menu-arrow">›</view>
        </view>
        
        <view class="menu-item" @click="handleLogout">
          <view class="menu-icon">🚪</view>
          <view class="menu-content">
            <text class="menu-title">退出登录</text>
            <text class="menu-desc">安全退出当前账户</text>
          </view>
          <view class="menu-arrow">›</view>
        </view>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">版本号：v1.0.0</text>
    </view>

    <!-- 自定义TabBar -->
    <CustomTabBar :current="2" @change="onTabChange" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import CustomTabBar from '@/components/CustomTabBar.vue'

// 用户信息接口
interface UserInfo {
  name?: string
  avatar?: string
  desc?: string
}

// 响应式数据
const isLoggedIn = ref(false)
const userInfo = reactive<UserInfo>({
  name: '',
  avatar: '',
  desc: ''
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 选择头像
const chooseAvatar = () => {
  if (!isLoggedIn.value) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      userInfo.avatar = res.tempFilePaths[0]
      uni.showToast({
        title: '头像更新成功',
        icon: 'success'
      })
    }
  })
}

// 处理登录
const handleLogin = () => {
  uni.showLoading({
    title: '登录中...'
  })
  
  // 模拟登录过程
  setTimeout(() => {
    uni.hideLoading()
    isLoggedIn.value = true
    userInfo.name = '微信用户'
    userInfo.desc = '欢迎使用我们的小程序'
    
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
  }, 1500)
}

// 处理退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        isLoggedIn.value = false
        userInfo.name = ''
        userInfo.avatar = ''
        userInfo.desc = ''
        
        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        })
      }
    }
  })
}

// 联系我们
const contactUs = () => {
  uni.showModal({
    title: '联系我们',
    content: '客服电话：400-123-4567\n邮箱：<EMAIL>\n工作时间：9:00-18:00',
    showCancel: false,
    confirmText: '知道了'
  })
}

// 设备调试
const deviceDebug = () => {
  uni.navigateTo({
    url: '/pages/debug/debug'
  })
}

// 用户设置
const userSettings = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

// TabBar切换
const onTabChange = (index: number) => {
  console.log('Tab changed to:', index)
}
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  padding-bottom: 120rpx; // 为TabBar留出空间
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  .nav-bar {
    display: flex;
    align-items: center;
    height: 88rpx;
    padding: 0 32rpx;
    
    .nav-left {
      width: 80rpx;
      
      .back-icon {
        font-size: 36rpx;
        color: #333;
        font-weight: bold;
      }
    }
    
    .nav-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
    
    .nav-right {
      width: 80rpx;
    }
  }
}

.user-section {
  background: #fff;
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  
  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    
    .avatar-container {
      position: relative;
      margin-right: 32rpx;
      
      .user-avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 60rpx;
        border: 4rpx solid #f0f0f0;
      }
      
      .avatar-edit {
        position: absolute;
        bottom: -8rpx;
        right: -8rpx;
        width: 48rpx;
        height: 48rpx;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .edit-icon {
          font-size: 24rpx;
        }
      }
    }
    
    .user-details {
      flex: 1;
      
      .user-name {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .user-desc {
        font-size: 28rpx;
        color: #666;
        line-height: 1.4;
      }
    }
  }
  
  .login-section {
    .login-btn {
      width: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 48rpx;
      padding: 24rpx 0;
      
      .login-text {
        color: #fff;
        font-size: 32rpx;
        font-weight: 600;
      }
    }
  }
}

.menu-section {
  margin: 0 32rpx;
  
  .menu-group {
    background: #fff;
    border-radius: 24rpx;
    margin-bottom: 32rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
    
    .menu-item {
      display: flex;
      align-items: center;
      padding: 32rpx;
      border-bottom: 2rpx solid #f5f5f5;
      transition: background-color 0.3s ease;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background-color: #f8f9fa;
      }
      
      .menu-icon {
        width: 80rpx;
        height: 80rpx;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        margin-right: 24rpx;
      }
      
      .menu-content {
        flex: 1;
        
        .menu-title {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .menu-desc {
          display: block;
          font-size: 26rpx;
          color: #666;
          line-height: 1.4;
        }
      }
      
      .menu-arrow {
        font-size: 32rpx;
        color: #ccc;
        font-weight: bold;
      }
    }
  }
}

.version-info {
  text-align: center;
  padding: 40rpx 0;
  
  .version-text {
    font-size: 24rpx;
    color: #999;
  }
}
</style>
