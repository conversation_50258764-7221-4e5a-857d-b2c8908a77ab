<template>
  <view class="custom-tabbar" v-if="showTabBar">
    <view class="tabbar-container">
      <view
        class="tab-item"
        v-for="(item, index) in tabList"
        :key="index"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <image
          class="tab-icon"
          :src="currentTab === index ? item.selectedIconPath : item.iconPath"
          mode="aspectFit"
        />
        <text class="tab-text" :class="{ active: currentTab === index }">
          {{ item.text }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 定义props
interface Props {
  current?: number
  showOnlyOnHome?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  current: 0,
  showOnlyOnHome: true
})

// 定义emits
const emit = defineEmits<{
  change: [index: number]
}>()

// 响应式数据
const currentTab = ref(props.current)

// TabBar配置
const tabList = [
  {
    text: '首页',
    iconPath: '/static/home.png',
    selectedIconPath: '/static/home_select.png',
    pagePath: '/pages/index/index'
  },
  {
    text: '案例',
    iconPath: '/static/case.png',
    selectedIconPath: '/static/case_select.png',
    pagePath: '/pages/case/case'
  },
  {
    text: '我的',
    iconPath: '/static/my.png',
    selectedIconPath: '/static/my_select.png',
    pagePath: '/pages/profile/profile'
  }
]

// 计算是否显示TabBar
const showTabBar = computed(() => {
  if (!props.showOnlyOnHome) return true

  // 获取当前页面路径
  const pages = getCurrentPages()
  if (pages.length === 0) return false

  const currentPage = pages[pages.length - 1]
  const currentRoute = currentPage.route || ''

  // 只在首页显示
  return currentRoute === 'pages/index/index'
})

// 切换Tab
const switchTab = (index: number) => {
  if (index === currentTab.value) return

  currentTab.value = index
  emit('change', index)

  const targetPage = tabList[index].pagePath

  // 所有页面都使用navigateTo，因为我们使用的是自定义TabBar
  if (index === 0) {
    // 如果是首页，使用reLaunch确保回到首页
    uni.reLaunch({
      url: targetPage
    })
  } else {
    uni.navigateTo({
      url: targetPage
    })
  }
}

// 监听页面变化更新当前tab
onMounted(() => {
  // 根据当前页面设置active状态
  const pages = getCurrentPages()
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage.route || ''

    const tabIndex = tabList.findIndex(item =>
      item.pagePath.includes(currentRoute.replace('pages/', '').replace('/index', ''))
    )

    if (tabIndex !== -1) {
      currentTab.value = tabIndex
    }
  }
})
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 30rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;

  .tabbar-container {
    display: flex;
    align-items: center;
    background: rgba(200, 200, 200, 0.95);
    backdrop-filter: blur(20rpx);
    border-radius: 60rpx;
    padding: 20rpx 40rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    border: 2rpx solid rgba(255, 255, 255, 0.2);

    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 16rpx 32rpx;
      margin: 0 8rpx;
      border-radius: 40rpx;
      transition: all 0.3s ease;
      position: relative;

      &.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transform: translateY(-4rpx);
        box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
      }

      .tab-icon {
        width: 48rpx;
        height: 48rpx;
        margin-bottom: 8rpx;
        transition: all 0.3s ease;
      }

      .tab-text {
        font-size: 24rpx;
        color: #666;
        transition: all 0.3s ease;
        font-weight: 500;

        &.active {
          color: #fff;
          font-weight: 600;
        }
      }

      // 点击效果
      &:active {
        transform: scale(0.95);
      }
    }
  }
}

// 适配不同屏幕尺寸
@media screen and (max-width: 750rpx) {
  .custom-tabbar {
    .tabbar-container {
      padding: 16rpx 32rpx;

      .tab-item {
        padding: 12rpx 24rpx;
        margin: 0 4rpx;

        .tab-icon {
          width: 40rpx;
          height: 40rpx;
          margin-bottom: 6rpx;
        }

        .tab-text {
          font-size: 22rpx;
        }
      }
    }
  }
}
</style>
