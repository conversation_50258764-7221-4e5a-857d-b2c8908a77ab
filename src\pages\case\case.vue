<template>
  <view class="case-page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <view class="nav-left" @click="goBack">
          <text class="back-icon">←</text>
        </view>
        <view class="nav-title">案例展示</view>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 案例列表 -->
    <scroll-view class="case-list" scroll-y>
      <view
        class="case-card"
        v-for="(item, index) in caseList"
        :key="index"
        @click="viewCase(item)"
      >
        <view class="card-image">
          <image :src="item.image" mode="aspectFill" class="case-image" />
          <view class="card-overlay">
            <text class="view-btn">查看详情</text>
          </view>
        </view>
        <view class="card-content">
          <view class="case-title">{{ item.title }}</view>
          <view class="case-desc">{{ item.description }}</view>
          <view class="case-meta">
            <view class="case-category">{{ item.category }}</view>
            <view class="case-date">{{ item.date }}</view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <text class="load-text">加载更多...</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" v-else>
        <text class="no-more-text">没有更多案例了</text>
      </view>
    </scroll-view>

    <!-- 自定义TabBar -->
    <CustomTabBar :current="1" @change="onTabChange" />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import CustomTabBar from '@/components/CustomTabBar.vue'

// 案例数据接口
interface CaseItem {
  id: number
  title: string
  description: string
  image: string
  category: string
  date: string
  content?: string
}

// 响应式数据
const caseList = ref<CaseItem[]>([])
const hasMore = ref(true)

// 模拟案例数据
const mockCaseData: CaseItem[] = [
  {
    id: 1,
    title: '智能家居控制系统',
    description: '基于微信小程序的智能家居控制解决方案，支持远程控制各种家电设备。',
    image: '/static/logo.png',
    category: '智能家居',
    date: '2024-01-15',
    content: '详细的案例介绍内容...'
  },
  {
    id: 2,
    title: '电商购物平台',
    description: '功能完整的电商小程序，包含商品展示、购物车、订单管理等功能。',
    image: '/static/logo.png',
    category: '电商平台',
    date: '2024-01-10',
    content: '详细的案例介绍内容...'
  },
  {
    id: 3,
    title: '在线教育系统',
    description: '支持视频课程、在线考试、学习进度跟踪的教育类小程序。',
    image: '/static/logo.png',
    category: '在线教育',
    date: '2024-01-05',
    content: '详细的案例介绍内容...'
  },
  {
    id: 4,
    title: '餐饮外卖平台',
    description: '集成点餐、支付、配送跟踪的餐饮外卖小程序解决方案。',
    image: '/static/logo.png',
    category: '餐饮服务',
    date: '2023-12-28',
    content: '详细的案例介绍内容...'
  },
  {
    id: 5,
    title: '健康管理助手',
    description: '个人健康数据记录、运动计划制定、健康建议推送的健康管理应用。',
    image: '/static/logo.png',
    category: '健康医疗',
    date: '2023-12-20',
    content: '详细的案例介绍内容...'
  }
]

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 查看案例详情
const viewCase = (caseItem: CaseItem) => {
  uni.showModal({
    title: caseItem.title,
    content: caseItem.description,
    showCancel: false,
    confirmText: '知道了'
  })
}

// TabBar切换
const onTabChange = (index: number) => {
  console.log('Tab changed to:', index)
}

// 加载案例数据
const loadCaseData = () => {
  // 模拟异步加载
  setTimeout(() => {
    caseList.value = mockCaseData
    hasMore.value = false
  }, 500)
}

// 页面加载
onMounted(() => {
  loadCaseData()
})
</script>

<style lang="scss" scoped>
.case-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  padding-bottom: 120rpx; // 为TabBar留出空间
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .nav-bar {
    display: flex;
    align-items: center;
    height: 88rpx;
    padding: 0 32rpx;

    .nav-left {
      width: 80rpx;

      .back-icon {
        font-size: 36rpx;
        color: #333;
        font-weight: bold;
      }
    }

    .nav-title {
      flex: 1;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }

    .nav-right {
      width: 80rpx;
    }
  }
}

.case-list {
  height: calc(100vh - 88rpx - 120rpx);
  padding: 32rpx;
}

.case-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }

  .card-image {
    position: relative;
    height: 400rpx;
    overflow: hidden;

    .case-image {
      width: 100%;
      height: 100%;
    }

    .card-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;

      .view-btn {
        background: rgba(255, 255, 255, 0.9);
        color: #333;
        padding: 16rpx 32rpx;
        border-radius: 40rpx;
        font-size: 28rpx;
        font-weight: 500;
      }
    }

    &:active .card-overlay {
      opacity: 1;
    }
  }

  .card-content {
    padding: 32rpx;

    .case-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 16rpx;
      line-height: 1.4;
    }

    .case-desc {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
      margin-bottom: 24rpx;
    }

    .case-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .case-category {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        padding: 8rpx 16rpx;
        border-radius: 16rpx;
        font-size: 24rpx;
      }

      .case-date {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.load-more,
.no-more {
  text-align: center;
  padding: 40rpx 0;

  .load-text,
  .no-more-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
