{
  // Vue相关配置
  "vue.codeActions.enabled": true,
  "vue.complete.casing.tags": "kebab",
  "vue.complete.casing.props": "camel",
  
  // TypeScript配置
  "typescript.preferences.quoteStyle": "single",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",
  
  // 编辑器配置
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  
  // 文件配置
  "files.eol": "\n",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.associations": {
    "*.vue": "vue",
    "*.nvue": "vue"
  },
  
  // Prettier配置
  "prettier.singleQuote": true,
  "prettier.semi": false,
  "prettier.trailingComma": "none",
  "prettier.printWidth": 100,
  
  // ESLint配置
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue"
  ],
  
  // 搜索排除
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/unpackage": true,
    "**/.git": true
  },
  
  // 文件监视排除
  "files.watcherExclude": {
    "**/node_modules/**": true,
    "**/dist/**": true,
    "**/unpackage/**": true
  },
  
  // Emmet配置
  "emmet.includeLanguages": {
    "vue-html": "html",
    "vue": "html"
  },
  
  // 终端配置
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  
  // 工作台配置
  "workbench.iconTheme": "vscode-icons",
  "workbench.colorTheme": "Default Dark+",
  
  // 智能提示
  "vetur.validation.template": false,
  "vetur.validation.script": false,
  "vetur.validation.style": false
}
