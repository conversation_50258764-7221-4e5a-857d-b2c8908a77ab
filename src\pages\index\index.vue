<template>
  <view class="content">
    <image class="logo" src="/static/logo.png" />
    <view class="text-area">
      <text class="title">{{ title }}</text>
      <text class="subtitle">基于uniapp的Vue 3项目脚手架</text>
    </view>

    <!-- 引入HelloWorld组件 -->
    <HelloWorld title="欢迎使用Vue 3!" />

    <!-- 计数器示例 -->
    <view class="counter-section">
      <text class="section-title">计数器示例</text>
      <view class="counter-display">
        <text class="count-text">当前计数: {{ count }}</text>
        <text class="count-info">双倍: {{ doubleCount }}</text>
        <text class="count-info">{{ isEven ? '偶数' : '奇数' }}</text>
      </view>
      <view class="button-group">
        <button @click="increment" class="btn btn-primary">+1</button>
        <button @click="decrement" class="btn btn-secondary">-1</button>
        <button @click="reset" class="btn btn-default">重置</button>
      </view>
    </view>

    <!-- 当前时间 -->
    <view class="time-section">
      <text class="section-title">当前时间</text>
      <text class="time-text">{{ currentTime }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import HelloWorld from '@/components/HelloWorld.vue'
import { useCounter } from '@/composables/useCounter'
import { formatDate } from '@/utils/index'

const title = ref('bux-weixin')

// 使用计数器组合式函数
const { count, doubleCount, isEven, increment, decrement, reset } = useCounter()

// 当前时间
const currentTime = ref('')
let timer: NodeJS.Timeout

const updateTime = () => {
  currentTime.value = formatDate(new Date())
}

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  min-height: 100vh;
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin-bottom: 30rpx;
}

.text-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.counter-section,
.time-section {
  width: 100%;
  max-width: 600rpx;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  text-align: center;
}

.counter-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.count-text {
  font-size: 36rpx;
  color: #007aff;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.count-info {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.btn {
  padding: 20rpx 30rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: white;
}

.btn-primary {
  background-color: #007aff;
}

.btn-secondary {
  background-color: #ff3b30;
}

.btn-default {
  background-color: #8e8e93;
}

.time-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  display: block;
}
</style>
